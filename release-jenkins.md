aries:
  job名: Aries-Deploy  
  参数列表:
    infraBranch: master（默认值）    
    AriesBranch: release tag，如LR/251.2.0    
canis:
  job名: Canis-K8S
  参数列表:
    EnvironmentName: open
    CanisCodeBranch: release tag，如LR/251.2.0
    InfraBranch: release分支，如release/251.2
em:
  job名: ProdCsmc
  参数列表:
    EnvironmentType: prod
    TenantName: veeva
    EksCluster: sfa-bj-prod-eks
    CodeBranch: release tag，如LR/251.2.0
    InfraCodeBranch: release分支，如release/251.2
    ImageTag: 可选项，默认不传
openlog:
  job名: Prod-K8S-Tracing
  参数列表:
    ClusterName: 默认sfa-bj-prod-eks，可不传
    Environment: 默认prod，可不传
pisces:
  job名: IaC-terraform-SageMaker
  参数列表:
    ACTION: 默认deploy，可选值deploy/clear
    InfraBranch: release分支，如release/251.2
    Region: 默认cn-north-1，可不传
    Tenants: 默认shared
chinacrm:
  job名: 租户名
  参数列表:
    TenantName: 租户名
    ProdCodeBranch: release tag，如LR/251.2.0
    NeedDeployPS: 默认false，可选值true/false
    InfraBranch: release分支，如release/251.2
    PSCodeBranch: release分支，如release/251.2，只有NeedDeployPS为true时才需要
    PSCodeBranch: 保留默认值，可不传
    DryRun: 默认false，一般不传
lumos:
  job名: lumos-k8s-deployment
  参数列表:
    Account: 默认prod，可不传
    EksCluster: 默认sfa-bj-prod-eks，可不传
    Region: 默认cn-north-1，可不传
    Environment: 默认prod，可不传
    Tenant: 租户名
    InfraBranch: release分支，如release/251.2
    Branch: release tag，如LR/251.2.0
    ImageTag: image tag
    DryRun: 默认false，一般不传    
    Node: 默认ec2-slave，可不传
taurus:
  job名: Prod-Taurus
  参数列表:
    TenantName: 租户名
    TaurusCodeBranch: release tag，如LR/251.2.0
    InfraBranch: release分支，如release/251.2  
    DryRun: 默认false，一般不传
rigel:
  job名: Prod-Rigel
  参数列表:
    TenantName: 租户名
    RigelCodeBranch: release tag，如LR/251.2.0
    InfraBranch: release分支，如release/251.2  
    DryRun: 默认false，一般不传    
hydra:
  job名: Hydra-Single-Tenant-Deploy-Increment
  参数列表:
    Tenant: 选择要执行的租户, 租户名全小写
    ProductDeploy: 默认true，可选值true/false
    ProductBranch: release tag，如LR/251.2.0
    PSDeploy: 默认false，可选值true/false
    PSBranch: release分支，如release/251.2,只有PSDeploy为true时才需要
    SlugFile: 如需执行部分 Migrate，则填写 --slugs xxx,xxx,xxx（逗号分隔）,如需执行全部 PS Migrate，则置空（删除 --slugs）
    FirePipelines: 默认false，可选值true/false，勾选后立即执行增量 pipeline，不能同时勾选 FullUpdate，全量更新时 pipeline 会失败
    infraBranch: release分支，如release/251.2
mintaka:
  job名: Prod-Mintaka
  参数列表:
    TenantName: 租户名
    ProdCodeBranch: release tag，如LR/251.2.0
    infraBranch: release分支，如release/251.2  
    Migrate: 默认true，可选值true/false
 