<div style="background-color: #f8f8f8;">
    <div style="background-color: #fff; width: 650px; margin: 0 auto;">
        <img src="{{ banner_up.src }}" style="width: 100%; height: 68px;">
        <div style="margin: 40px 50px 60px; font-family: SourceHanSansSC; font-size: 14px;">
            <p>尊敬的用户：</p>
            <p>
                为了进一步优化和规范Veeva产品环境的使用，提升整体服务质量与效率，我们计划进行一项环境调整。在此通知您，预部署环境预计将在2025年6月（25R1.2 升级）正式下线。 
                环境下线后，您将无法继续访问该环境及其相关服务。
                请您特别注意，预部署环境的下线并不会影响预发布流程的正常实施。 您仍然可以在收到新版本开放预览与验证的邮件通知后，通过“环境管理”功能，将您任一指定的标准沙盒环境升级至最新的产品版本，以便提前预览和验证新功能。
            </p>  
            <p>
                感谢您一直以来对 Veeva 产品的支持。如有任何疑问，请通过
                <a style="color: #f89728" href="https://support.veeva.com/hc/en-us">Veeva Support Portal</a>
                联系产品支持团队进行确认。
            </p>
            </p>
            <p>
                In order to further optimize and standardize the use of Veeva product environments and improve overall service quality and efficiency, we are planning an environment adjustment.
                We would like to inform you that the pre-deploy environment is expected to be officially retired in June 2025 (25R1.2 release). 
                After the environment is retired, you will no longer be able to access the environment and its related services.
                Please note that the retirement of pre-deploy environment will not affect the normal implementation of pre-deploy process. Once you receive an email notification that new release is open for preview and validation, 
                you can still upgrade any of your designated standard sandbox to the latest product release via the Environment Management to preview and validate new features.
            <p>
                Thank you for your continued support of Veeva products. For any question, please contact China CRM Suite Support team via <a style="color: #f89728"
                    href="https://support.veeva.com/hc/en-us">Veeva Support Portal</a>.
            </p>
            </p> 
        </div>
        <img src="{{ banner_down.src }}" style="width: 100%; height: 71px;">
    </div>
</div>