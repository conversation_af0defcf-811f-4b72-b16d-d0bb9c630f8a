def node_label = (params.Node) ? params.Node : "ec2-slave"

// 检查cron表达式是否在发布窗口内
def checkCronInWindow(cronExpression, startHour, startMinute, endHour, endMinute) {
    try {
        // 解析cron表达式 (分 时 日 月 周)
        def parts = cronExpression.split(' ')
        if (parts.size() < 5) {
            return false
        }
        
        def minute = parts[0]
        def hour = parts[1]
        
        // 检查是否为高频任务（执行频率等于或高于每小时一次）
        if (isHighFrequencyTask(minute, hour)) {
            return false
        }
        
        // 检查小时和分钟是否在窗口内
        if (hour.contains('/')) {
            // 处理类似 */2 的情况（每2小时执行一次）
            def interval = hour.split('/')[1].toInteger()
            for (def h = 0; h < 24; h += interval) {
                if (isTimeInWindow(h, 0, startHour, startMinute, endHour, endMinute)) {
                    return true
                }
            }
        } else if (hour.contains('-')) {
            // 处理类似 8-17 的情况
            def range = hour.split('-')
            def fromHour = range[0].toInteger()
            def toHour = range[1].toInteger()
            for (def h = fromHour; h <= toHour; h++) {
                if (isTimeInWindow(h, 0, startHour, startMinute, endHour, endMinute)) {
                    return true
                }
            }
        } else if (hour.contains(',')) {
            // 处理类似 8,9,10 的情况
            def hours = hour.split(',')
            for (def h in hours) {
                if (isTimeInWindow(h.toInteger(), 0, startHour, startMinute, endHour, endMinute)) {
                    return true
                }
            }
        } else {
            // 单一小时值
            def h = hour.toInteger()
            return isTimeInWindow(h, 0, startHour, startMinute, endHour, endMinute)
        }
        
        return false
    } catch (Exception e) {
        echo "解析cron表达式出错: ${e.message}"
        return false
    }
}

// 检查是否为高频任务（执行频率等于或高于每两小时一次）
def isHighFrequencyTask(minute, hour) {
    // 如果分钟字段包含 */n 且 n < 60，则为高频任务
    if (minute.contains('*/')) {
        def interval = minute.split('/')[1].toInteger()
        if (interval < 60) {
            return true
        }
    }
    
    // 如果分钟字段包含多个值或范围，可能是高频任务
    if (minute.contains(',') || minute.contains('-')) {
        return true
    }
    
    // 如果分钟字段为 *，则每分钟执行一次，是高频任务
    if (minute == '*') {
        return true
    }
    
    // 如果小时字段包含 */n 且 n <= 2，则为高频任务（每两小时或更频繁）
    if (hour.contains('*/')) {
        def interval = hour.split('/')[1].toInteger()
        if (interval <= 2) {
            return true
        }
    }
    
    // 如果小时字段为 *，则每小时执行一次，是高频任务
    if (hour == '*') {
        return true
    }
    
    // 如果小时字段包含逗号，检查是否有连续的小时（间隔小于等于2小时）
    if (hour.contains(',')) {
        def hours = hour.split(',').collect { it.toInteger() }.sort()
        for (int i = 0; i < hours.size() - 1; i++) {
            // 检查相邻小时之间的间隔
            def diff = hours[i+1] - hours[i]
            // 考虑跨天的情况
            if (diff > 12) {
                diff = 24 - diff
            }
            if (diff <= 2) {
                return true
            }
        }
        // 检查第一个和最后一个小时之间的间隔（考虑跨天的情况）
        if (hours.size() > 1) {
            def diff = (hours[0] + 24) - hours[-1]
            if (diff <= 2) {
                return true
            }
        }
    }
    
    // 如果小时字段包含范围，检查范围内的间隔
    if (hour.contains('-')) {
        def range = hour.split('-')
        def fromHour = range[0].toInteger()
        def toHour = range[1].toInteger()
        
        // 如果范围跨度大于12小时，可能是跨天的情况
        if (toHour - fromHour > 12) {
            // 跨天的情况，检查是否有连续的小时
            if ((fromHour + 24) - toHour <= 2) {
                return true
            }
        } else {
            // 正常情况，检查范围内的间隔
            if (toHour - fromHour >= 2) {
                return true
            }
        }
    }
    
    // 其他情况（单一分钟值且小时不是高频执行）不是高频任务
    return false
}

// 转义CSV字段中的特殊字符
def escapeCSV(field) {
    if (field == null) return '""'
    
    // 如果字段包含逗号、双引号或换行符，需要用双引号包围并转义内部的双引号
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
        return '"' + field.replaceAll('"', '""') + '"'
    }
    return field
}

// 检查时间是否在窗口内
def isTimeInWindow(hour, minute, startHour, startMinute, endHour, endMinute) {
    def timeInMinutes = hour * 60 + minute
    def startInMinutes = startHour * 60 + startMinute
    def endInMinutes = endHour * 60 + endMinute
    
    // 处理跨天的情况
    if (endInMinutes < startInMinutes) {
        return timeInMinutes >= startInMinutes || timeInMinutes <= endInMinutes
    } else {
        return timeInMinutes >= startInMinutes && timeInMinutes <= endInMinutes
    }
}


// 检查当前日期是否是指定日期前的第N天
def isDaysBeforeDate(targetDate, daysBeforeCount) {
    try {
        // 使用 java.text.SimpleDateFormat 解析日期
        def sdf = new java.text.SimpleDateFormat("yyyy-MM-dd")
        sdf.setLenient(false)  // 严格模式，不允许无效日期
        
        // 解析目标日期
        def targetDateObj = sdf.parse(targetDate)
        
        // 获取当前日期（去除时间部分）
        def today = new Date()
        def todayStr = sdf.format(today)
        today = sdf.parse(todayStr)
        
        // 计算日期差异（毫秒）
        def diff = targetDateObj.time - today.time
        
        // 转换为天数（1天 = 24小时 * 60分钟 * 60秒 * 1000毫秒）
        def diffDays = diff / (24 * 60 * 60 * 1000)
        
        // 检查是否是目标日期前的第N天
        return diffDays == daysBeforeCount
    } catch (Exception e) {
        echo "解析日期出错: ${e.message}"
        return false
    }
}

pipeline {
    agent { label node_label }
    
    stages {
        stage('检查定时任务') {
            steps {
                script {
                    // 检查是否需要发送邮件
                    def shouldSendEmail = false
                    
                    // 检查是否提供了发布日期参数
                    if (params.next_release_date) {
                        echo "发布日期: ${params.next_release_date}"
                        // 获取提前通知的天数，默认为7天
                        def daysBeforeRelease = params.days_before_release ? params.days_before_release.toInteger() : 7
                        shouldSendEmail = isDaysBeforeDate(params.next_release_date, daysBeforeRelease)
                        if (shouldSendEmail) {
                            echo "今天是发布日期前${daysBeforeRelease}天，将发送邮件通知"
                        } else {
                            echo "今天不是发布日期前${daysBeforeRelease}天，不发送邮件通知"
                        }
                    } else {
                        echo "未提供发布日期参数，将正常发送邮件"
                        shouldSendEmail = true
                    }
                    
                    // 加载YAML文件
                    def releaseWindows = readYaml file: 'release_windows.yaml'
                    
                    // 直接从环境变量获取数据库连接信息
                    def dbHost = env.CustomerMetaDBHost
                    def dbUsername = env.CustomerMetaDBUsername
                    def dbPassword = env.CustomerMetaDBPassword
                    
                    echo "使用数据库连接信息: Host=${dbHost}, Username=${dbUsername}"
                    
                    // 获取任务确认列表
                    def canBeIgnoredList = []
                    def canNotBeIgnoredList = []
                    try {
                        // 从YAML文件读取任务确认列表
                        def confirmedTasksFile = 'tools/scheduled-tasks-scan/confirmed_tasks.yaml'
                        if (fileExists(confirmedTasksFile)) {
                            def confirmedTasks = readYaml file: confirmedTasksFile
                            canBeIgnoredList = confirmedTasks.can_be_ignored ?: []
                            canNotBeIgnoredList = confirmedTasks.can_not_be_ignored ?: []
                            echo "从文件读取任务确认列表: ${canBeIgnoredList.size()} 个可忽略任务, ${canNotBeIgnoredList.size()} 个已确认任务"
                        } else {
                            echo "任务确认列表文件不存在: ${confirmedTasksFile}"
                        }
                    } catch (Exception e) {
                        echo "读取任务确认列表文件出错: ${e.message}"
                    }
                    
                    // 分割客户列表
                    def customers = params.next_release_customers.split(',')
                    
                    // 保存结果的Map
                    def tasksInReleaseWindow = [:]
                    
                    // 创建临时密码文件（使用writeFile避免在日志中显示密码）
                    def tempConfigFile = "${WORKSPACE}/mysql_config_${BUILD_NUMBER}.cnf"
                    writeFile file: tempConfigFile, text: """[client]
password=${dbPassword}
"""
                    sh "chmod 600 ${tempConfigFile}"
                    
                    try {
                        // 首先获取所有数据库名称列表
                        def allDatabases = sh(script: """
                            # 使用配置文件连接MySQL
                            mysql --defaults-file=${tempConfigFile} -h ${dbHost} -u ${dbUsername} -e "SHOW DATABASES" --batch --skip-column-names
                        """, returnStdout: true).trim().split('\n')
                        
                        // 创建客户名到数据库名的映射
                        def customerToDbMap = [:]
                        allDatabases.each { db ->
                            customers.each { customer ->
                                if (db.toLowerCase() == customer.toLowerCase()) {
                                    customerToDbMap[customer] = db
                                }
                            }
                        }
                        
                        // 循环处理每个客户
                        customers.each { customer ->
                            echo "正在检查客户: ${customer}"
                            
                            // 获取该客户的发布窗口时间
                            def releaseWindow = releaseWindows[customer.toLowerCase()]
                            if (releaseWindow) {
                                // 解析发布窗口时间
                                def (startTime, endTime) = releaseWindow.replace('"', '').split('-')
                                def startHour = startTime.split(':')[0].toInteger()
                                def startMinute = startTime.split(':')[1].toInteger()
                                def endHour = endTime.split(':')[0].toInteger()
                                def endMinute = endTime.split(':')[1].toInteger()
                                
                                // 如果结束时间是00:00，将其视为24:00
                                if (endHour == 0 && endMinute == 0) {
                                    endHour = 24
                                }
                                
                                echo "发布窗口时间: ${startHour}:${startMinute} - ${endHour}:${endMinute}"
                                
                                // 获取正确的数据库名称（考虑大小写）
                                def dbName = customerToDbMap[customer] ?: customer
                                echo "使用数据库名称: ${dbName}"
                                
                                // 连接数据库并查询，联动tasks表，只查询enable=1的行
                                def sql = """
                                    SELECT cj.id, cj.name, cj.cron, cj.description, t.queue 
                                    FROM ${dbName}.cron_jobs cj
                                    LEFT JOIN ${dbName}.tasks t ON cj.task_id = t.id
                                    WHERE cj.enable = 1
                                """
                                
                                try {
                                    def result = sh(script: """
                                        # 使用配置文件连接MySQL
                                        mysql --defaults-file=${tempConfigFile} -h ${dbHost} -u ${dbUsername} -e "${sql}" --batch --skip-column-names
                                    """, returnStdout: true).trim()
                                    
                                    if (result) {
                                        def rows = result.split('\n')
                                        echo "客户 ${customer} 有 ${rows.size()} 个启用的定时任务"
                                        
                                        def highFrequencyTasks = 0
                                        def ignoredTasks = 0
                                        def inWindowTasks = 0
                                        
                                        rows.each { row ->
                                            def columns = row.split('\t')
                                            if (columns.size() >= 5) {
                                                def id = columns[0]
                                                def name = columns[1]
                                                def cronExpression = columns[2]
                                                def description = columns[3] ?: ""
                                                def queue = columns[4] ?: ""
                                                
                                                // 检查任务是否在可忽略列表中
                                                if (canBeIgnoredList.contains(name)) {
                                                    echo "任务 ${name} 在可忽略列表中，跳过检查"
                                                    ignoredTasks++
                                                    return // 跳过此任务
                                                }
                                                
                                                // 检查是否为高频任务
                                                def parts = cronExpression.split(' ')
                                                if (parts.size() >= 5) {
                                                    def minute = parts[0]
                                                    def hour = parts[1]
                                                    if (isHighFrequencyTask(minute, hour)) {
                                                        highFrequencyTasks++
                                                        return // 跳过高频任务
                                                    }
                                                }
                                                
                                                // 解析cron表达式
                                                def isInWindow = checkCronInWindow(cronExpression, startHour, startMinute, endHour, endMinute)
                                                
                                                if (isInWindow) {
                                                    inWindowTasks++
                                                    if (!tasksInReleaseWindow.containsKey(customer)) {
                                                        tasksInReleaseWindow[customer] = []
                                                    }
                                                    
                                                    // 确定任务状态
                                                    def status = canNotBeIgnoredList.contains(name) ? "已确认" : "待确认"
                                                    
                                                    tasksInReleaseWindow[customer] << [
                                                        id: id, 
                                                        name: name, 
                                                        cron: cronExpression,
                                                        description: description,
                                                        queue: queue,
                                                        status: status
                                                    ]
                                                }
                                            }
                                        }
                                        
                                        echo "客户 ${customer} 有 ${highFrequencyTasks} 个高频任务被筛除，${ignoredTasks} 个任务被忽略，${inWindowTasks} 个任务在发布窗口内"
                                    } else {
                                        echo "客户 ${customer} 没有启用的定时任务"
                                    }
                                } catch (Exception e) {
                                    echo "查询客户 ${customer} 的定时任务时出错: ${e.message}"
                                }
                            } else {
                                echo "警告: 未找到客户 ${customer} 的发布窗口时间"
                            }
                        }
                    } finally {
                        // 确保在任何情况下都删除临时密码文件
                        sh "rm -f ${tempConfigFile}"
                    }
                    
                    // 输出结果
                    def output = new StringBuilder()
                    output.append("=== 定时任务检查结果汇总 ===\n\n")
                    output.append("注意：已筛除执行频率等于或高于每两小时一次的高频任务\n")
                    if (!canBeIgnoredList.isEmpty()) {
                        output.append("同时忽略了以下任务: ${canBeIgnoredList.join(', ')}\n")
                    }
                    output.append("\n")
                    
                    // 同时创建CSV格式的输出
                    def csvOutput = new StringBuilder()
                    csvOutput.append("客户,任务ID,队列,Cron表达式,任务名称,描述,状态\n")
                    
                    // 创建HTML格式的邮件内容
                    def htmlOutput = new StringBuilder()
                    htmlOutput.append("<html><head><style>")
                    htmlOutput.append("body { font-family: Arial, sans-serif; }")
                    htmlOutput.append("h1 { font-size: 18px; color: #333; }")
                    htmlOutput.append("h2 { font-size: 16px; color: #333; margin-top: 20px; }")
                    htmlOutput.append("p { margin: 5px 0; }")
                    htmlOutput.append("table { border-collapse: collapse; width: 100%; margin: 15px 0; }")
                    htmlOutput.append("th { background-color: #f2f2f2; font-weight: bold; text-align: left; padding: 8px; border: 1px solid #ddd; }")
                    htmlOutput.append("td { padding: 8px; border: 1px solid #ddd; }")
                    htmlOutput.append("tr:nth-child(even) { background-color: #f9f9f9; }")
                    htmlOutput.append(".warning { color: #ff6600; }")
                    htmlOutput.append(".confirmed { color: #006600; font-weight: bold; background-color: #e6ffe6; border: 1px solid #00cc00; padding: 3px 8px; border-radius: 3px; }")
                    htmlOutput.append(".pending { color: #cc0000; font-weight: bold; background-color: #fff0f0; border: 1px solid #ff6666; padding: 3px 8px; border-radius: 3px; }")
                    htmlOutput.append("</style></head><body>")
                    
                    // 添加标题
                    htmlOutput.append("<h1>=== 定时任务检查结果汇总 ===</h1>")
                    htmlOutput.append("<p>注意：已筛除执行频率等于或高于每两小时一次的高频任务</p>")
                    if (!canBeIgnoredList.isEmpty()) {
                        htmlOutput.append("<p>同时忽略了以下任务: ${canBeIgnoredList.join(', ')}</p>")
                    }
                    
                    if (tasksInReleaseWindow.isEmpty()) {
                        output.append("没有发现在发布窗口内执行的定时任务\n")
                        htmlOutput.append("<p>没有发现在发布窗口内执行的定时任务</p>")
                    } else {
                        tasksInReleaseWindow.each { customer, tasks ->
                            def releaseWindow = releaseWindows[customer.toLowerCase()] ?: "未知"
                            
                            output.append("客户: ${customer}\n")
                            output.append("发布窗口时间: ${releaseWindow}\n\n")
                            
                            // HTML格式的客户信息
                            htmlOutput.append("<h2>客户: ${customer}</h2>")
                            htmlOutput.append("<p>发布窗口时间: ${releaseWindow}</p>")
                            
                            if (tasks.isEmpty()) {
                                output.append("无任务在发布窗口内执行\n")
                                htmlOutput.append("<p>无任务在发布窗口内执行</p>")
                            } else {
                                output.append("以下定时任务在发布窗口内执行:\n\n")
                                
                                // 创建表头
                                output.append("+----------------------------------+------------+--------------------+--------------------------------+--------------------------------+------------+\n")
                                output.append("| 任务ID                           | 队列        | Cron表达式         | 任务名称                       | 描述                           | 状态       |\n")
                                output.append("+----------------------------------+------------+--------------------+--------------------------------+--------------------------------+------------+\n")
                                
                                // HTML表格
                                htmlOutput.append("<table>")
                                htmlOutput.append("<tr>")
                                htmlOutput.append("<th>任务ID</th>")
                                htmlOutput.append("<th>队列</th>")
                                htmlOutput.append("<th>Cron表达式</th>")
                                htmlOutput.append("<th>任务名称</th>")
                                htmlOutput.append("<th>描述</th>")
                                htmlOutput.append("<th>状态</th>")
                                htmlOutput.append("</tr>")
                                
                                // 为每个任务创建一行
                                tasks.each { task ->
                                    def id = task.id.toString()
                                    def queue = (task.queue ?: '无')
                                    def cron = task.cron
                                    def name = task.name
                                    def desc = (task.description ?: '无')
                                    def status = task.status
                                    
                                    // 直接输出完整字段，不再截断
                                    output.append("| ${id} | ${queue} | ${cron} | ${name} | ${desc} | ${status} |\n")
                                    
                                    // 添加到CSV输出
                                    // 处理CSV中的特殊字符
                                    def escapedCustomer = escapeCSV(customer)
                                    def escapedId = escapeCSV(id)
                                    def escapedQueue = escapeCSV(queue)
                                    def escapedCron = escapeCSV(cron)
                                    def escapedName = escapeCSV(name)
                                    def escapedDesc = escapeCSV(desc)
                                    def escapedStatus = escapeCSV(status)
                                    
                                    csvOutput.append("${escapedCustomer},${escapedId},${escapedQueue},${escapedCron},${escapedName},${escapedDesc},${escapedStatus}\n")
                                    
                                    // HTML表格行
                                    htmlOutput.append("<tr>")
                                    htmlOutput.append("<td>${id}</td>")
                                    htmlOutput.append("<td>${queue}</td>")
                                    htmlOutput.append("<td>${cron}</td>")
                                    htmlOutput.append("<td>${name}</td>")
                                    htmlOutput.append("<td>${desc}</td>")
                                    
                                    // 根据状态设置不同的样式
                                    if (status == "已确认") {
                                        htmlOutput.append("<td><span class='confirmed'>${status}</span></td>")
                                    } else {
                                        htmlOutput.append("<td><span class='pending'>${status}</span></td>")
                                    }
                                    
                                    htmlOutput.append("</tr>")
                                }
                                
                                // 添加表格底部边框
                                output.append("+----------------------------------+------------+--------------------+--------------------------------+--------------------------------+------------+\n")
                                output.append("\n")
                                
                                // 关闭HTML表格
                                htmlOutput.append("</table>")
                            }
                            output.append("\n")
                        }
                    }
                    output.append("=== 检查完成 ===\n")
                    
                    // 添加HTML结尾
                    htmlOutput.append("<h1>=== 检查完成 ===</h1>")
                    htmlOutput.append("</body></html>")
                    
                    // 一次性输出所有结果
                    def resultOutput = output.toString()
                    echo resultOutput
                    
                    // 保存CSV文件
                    def csvFileName = "scheduled_tasks_${BUILD_NUMBER}.csv"
                    writeFile file: csvFileName, text: csvOutput.toString()
                    
                    // 发送HTML格式的邮件
                    def emailReceiver = env.MAIL_RECEIVER
                    
                    if (emailReceiver && shouldSendEmail) {
                        try {
                            // 发送邮件
                            mail subject: "定时任务检查结果",
                                 body: htmlOutput.toString(),
                                 charset: 'UTF-8',
                                 mimeType: 'text/html',
                                 to: emailReceiver
                            
                            echo "已将结果发送到邮箱: ${emailReceiver}"
                        } catch (Exception e) {
                            echo "发送邮件失败: ${e.message}"
                        }
                    } else if (!shouldSendEmail) {
                        echo "根据发布日期检查，跳过发送邮件"
                    } else {
                        echo "未配置 MAIL_RECEIVER 环境变量，跳过发送邮件"
                    }
                }
            }
        }
    }
}