#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
共享配置文件
"""

# JIRA 配置
JIRA_URL = "https://jira.veevadev.com"
JIRA_API_PATH = "/rest/api/2"
JIRA_ISSUE_API = f"{JIRA_URL}{JIRA_API_PATH}/issue"
JIRA_CREATEMETA_API = f"{JIRA_URL}{JIRA_API_PATH}/issue/createmeta/OOPS/issuetypes/10"

# 数据库配置
DB_CONFIG = {
    "host": "#RELEASE_INFO_DB_HOST#",
    "database": "#RELEASE_INFO_DB_NAME#",
    "user": "#RELEASE_INFO_DB_USER#",
    "password": "#RELEASE_INFO_DB_PASSWORD#",
    "port": 3306
}