#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集中管理模板（Prod和Preview环境）
"""

# 通用JIRA工单模板结构
JIRA_TEMPLATE = '''
{
    "fields": {
        "project": {"key": "OOPS"},
        "issuetype": {"id": "10"},
        "summary": "{{ version_type }}/{{ release }} {% if deploy_stage == 'Prod' %}正式部署{% else %}预部署{% endif %}",
        "description": {{ description_content|tojson }},
        "priority": {"id": "3"},
        "duedate": "{{ deploy_date }}",
        "customfield_17796": {"value": "Customer"},
        "customfield_17547": {"value": "Code Change"},
        "fixVersions": [{"name": "{{ release }} - {{ version_type }}"}],
        "customfield_11032": [{% for customer in valid_customers_by_metadata %}{"value": "{{ customer }}"}{% if not loop.last %},{% endif %}{% endfor %}]
    }
}
'''

# 描述模板
DESCRIPTION_TEMPLATE = '''
{panel:title=提单须知}
 * 申请 *新建 China CRM/Hydra/Lumos 环境* 或 {*}调整沙盒数据库{*}，请参考 [ChinaSFA环境规范与流程-工单模版|https://wiki.veevadev.com/pages/viewpage.action?pageId=387965158#ChinaSFA%E7%8E%AF%E5%A2%83%E8%A7%84%E8%8C%83%E4%B8%8E%E6%B5%81%E7%A8%8B-%E5%88%9B%E5%BB%BA%E7%94%9F%E4%BA%A7%E7%8E%AF%E5%A2%83ticket%E6%A8%A1%E7%89%88] 提供信息
 * 提单人需要在可执行时将工单置为 *Ready* 状态
 * [工单审批流程参考文档|https://wiki.veevadev.com/pages/viewpage.action?pageId=640133016]{panel}
{*}业务背景{*}：{{ version_type }}/{{ release }} {{ deploy_stage }} Deployment

{*}环境/租户{*}：{% if deploy_stage == 'Prod' %}Prod{% else %}Staging{% endif %}

{*}部署日期{*}：{{ deploy_date }}

{*}具体需求{*}：
||服务||部署 tag||是否发布||
{%- for service in service_list %}
|{{ service }}|{{ version_type }}/{{ tag_version }}{% if deploy_stage == 'Preview' %}-rc{*}X{*}{% endif %}|是|
{%- endfor %}


 
||客户列表||状态||部署租户||部署分支||是否部署ps代码||是否部署hydra||升级窗口||额外操作步骤||
{%- for customer in customer_values %}
|{{ customer.name }}/{{ customer.value }}|{{ customer.status }}|{{ customer.tenant }}|{{ customer.branch }}|{{ customer.ps_code }}|{{ customer.with_hydra }}|{{ customer.window }}|{{ customer.extra_steps }}|
{%- endfor %}
'''

# 子任务模板
SUBTASK_TEMPLATE = '''
{
    "fields": {
        "project": {"key": "OOPS"},
        "parent": {"key": "{{ parent_key }}"},
        "summary": "{{ summary }}",
        "issuetype": {"id": "{{ issuetype_id }}"},
        "priority": {"id": "{{ priority_id }}"},
        "customfield_10131": "{{ deploy_date }}",
        "fixVersions": [{"name": "{{ release }} - {{ version_type }}"}]
        {%- if assignee and assignee != "unassigned" -%}
        ,
        "assignee": {"name": "{{ assignee }}"}
        {%- endif -%}
        {%- if description and description|trim -%}
        ,
        "description": "{{ description }}"
        {%- endif -%}
    }
}
'''

# 生产环境子任务列表
PROD_SUBTASKS = [
    {
        "summary_template": "【Pre】给线上客户发送生产部署通知",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】确定是否有 infrastructure code change",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】Migration 效率检查和优化",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】确定所有客户代码中的改动已经合并到 master 分支",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】QA Sign-Off",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】合并release/{{ release_version }} 分支并打tag {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 lumos【14:00-15:00】不停机",
        "description": "AW 集成\r\n * lumos-prod-ucb  \r\n\r\nChina CRM 集成：\r\n * lumos-prod-bms\r\n * lumos-prod-msd\r\n\r\ncc [~<EMAIL>] [~<EMAIL>] [~<EMAIL>] [~<EMAIL>] [~<EMAIL>] \r\n\r\n请部署后手动更新客户环境列表中的信息，当前smartengine 不能自动更新wiki 中版本信息。",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "lumos",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 EM【17:00-19:00】不停机",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "em",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 Pavo【17:00-19:00】",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "pavo",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 OpenLog【17:00-19:00】",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "openlog",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 Dataloader",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "dataloader",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 Pisces",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "pisces",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署 Aries",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "aries",
        "type": "common"
    },
    {
        "summary_template": "【Pre】部署准生产和内部环境【17:00-19:00】",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【CRM】部署 China CRM {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Taurus】部署 taurus {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "taurus",
        "type": "common"
    },
    {
        "summary_template": "【Rigel】部署 Rigel {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "rigel",
        "type": "common"
    },
    {
        "summary_template": "【Hydra】部署 Hydra {{ version_type }}/{{ tag_version }}",
        "description": "部署参数\r\n * 勾选 ProductDeploy\r\n * 填写 ProductBranch 为 release tag\r\n * infraBranch 填写 release 分支\r\n * 其余选项不勾选\r\n\r\n[~<EMAIL>] [~<EMAIL>] [~<EMAIL>]",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "hydra",
        "type": "common"
    },
    {
        "summary_template": "【Mintaka】部署 Mintaka {{ version_type }}/{{ tag_version }}（hydra升级之后）",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "mintaka",
        "type": "common"
    },
    {
        "summary_template": "【Canis】部署 Canis {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "canis",
        "type": "common"
    },
    {
        "summary_template": "【Post】部署UATCanis {{ version_type }}/{{ tag_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "canis",
        "type": "common"
    },
    {
        "summary_template": "【CRM】为 MSD 部署 China CRM {{ version_type }}/{{ tag_version }} ***于【{{ msd_deploy_date }}】部署***",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Taurus】为 MSD 部署 Taurus {{ version_type }}/{{ tag_version }} ***于【{{ msd_deploy_date }}】部署***",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "taurus",
        "type": "common"
    },
    {
        "summary_template": "【Rigel】为 MSD 部署 Rigel {{ version_type }}/{{ tag_version }} ***于【{{ msd_deploy_date }}】部署***",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "rigel",
        "type": "common"
    },
    {
        "summary_template": "【Hydra】为 MSD 部署 Hydra {{ version_type }}/{{ tag_version }} ***于【{{ msd_deploy_date }}】部署***",
        "description": "部署参数\r\n * 勾选 ProductDeploy\r\n * 填写 ProductBranch 为 release tag\r\n * infraBranch 填写 release 分支\r\n * 其余选项不勾选\r\n\r\n[~<EMAIL>] [~<EMAIL>] [~<EMAIL>]",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "hydra",
        "type": "common"
    },
    {
        "summary_template": "【Mintaka】为 MSD 部署 Mintaka {{ version_type }}/{{ tag_version }} ***于【{{ msd_deploy_date }}】（hydra升级之后）部署***",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "mintaka",
        "type": "common"
    },
    {
        "summary_template": "【Post】更新环境列表",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Post】给上线客户发送部署完成通知",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Post】合并分支",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Post】升级Commons数据库（仅GR）",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "GR"
    }
]

# 预览环境子任务列表
PREVIEW_SUBTASKS = [
    {
        "summary_template": "【Pre】Canis兼容性测试",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee":  "<EMAIL>",
        "related_service": "canis",
        "type": "common"
    },
    {
        "summary_template": "【Pre】确定是否有 infrastructure code change",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】chinasfa-infrastructure 仓库基于 master 分支创建分支 release/{{ release_version }}",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】在 release/{{ release_version }} 创建 {{ version_type }}/{{ tag_version }}-rcX tag",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】确定所有客户代码中的改动已经合并到预部署的 release 分支",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Pre】更新工单描述中各个服务的最新 rc tag 和是否部署 ps 信息",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【CRM】部署 China CRM {{ version_type }}/{{ tag_version }}-rcX",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    },
    {
        "summary_template": "【Taurus】部署 Taurus {{ version_type }}/{{ tag_version }}-rcX (仅默沙东)",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "taurus",
        "type": "common"
    },
    {
        "summary_template": "【Rigel】部署 Rigel {{ version_type }}/{{ tag_version }}-rcX (仅默沙东)",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "rigel",
        "type": "common"
    },
    {
        "summary_template": "【Hydra】部署 Hydra {{ version_type }}/{{ tag_version }}-rcX (仅默沙东)",
        "description": "部署参数\r\n * 勾选 ProductDeploy\r\n * 填写 ProductBranch 为 release tag\r\n * infraBranch 填写 release 分支\r\n * 其余选项不勾选\r\n\r\n[~<EMAIL>] [~<EMAIL>] [~<EMAIL>]",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "hydra",
        "type": "common"
    },
    {
        "summary_template": "【Mintaka】部署 Mintaka {{ version_type }}/{{ tag_version }}-rcX (hydra部署后)(仅默沙东)",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "mintaka",
        "type": "common"
    },
    {
        "summary_template": "【Canis】部署 Staging-Canis {{ version_type }}/{{ tag_version }}-rcX",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "canis",
        "type": "common"
    },
    {
        "summary_template": "【Post】部署 UATCanis {{ version_type }}/{{ tag_version }}-rcX",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "canis",
        "type": "common"
    },
    {
        "summary_template": "【Post】给上线客户发送部署完成通知",
        "issuetype_id": "8",
        "priority_id": "3",
        "assignee": "<EMAIL>",
        "related_service": "chinacrm",
        "type": "common"
    }
]

# 根据环境选择对应的子任务列表
def get_subtasks(deploy_stage):
    """
    根据部署环境获取对应的子任务列表
    
    Args:
        deploy_stage: 部署环境 (Prod/Preview)
        
    Returns:
        list: 子任务列表
    """
    if deploy_stage.lower() == 'prod':
        return PROD_SUBTASKS
    else:
        return PREVIEW_SUBTASKS