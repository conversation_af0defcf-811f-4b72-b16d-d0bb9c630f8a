def node_label = (params.Node) ? params.Node : "ec2-slave"

pipeline {
    agent { label node_label }
    
    stages {
        stage('准备环境') {
            steps {
                script {
                    env.PIP_MIRROR = "https://mirrors.devops.veevasfa.cn/repository/pypi-all/simple"
                    env.WORK_DIR = "${WORKSPACE}/tools/create-release-jira-ticket"
                    env.IssueKey = params.IssueKey.replaceAll(',$', '')
                    env.WITH_SUBTASKS_PARAM = params.WithSubtasks ? "--with-subtasks" : ""
                    env.VENV_PATH = "${WORKSPACE}/tools/create-release-jira-ticket/venv"

                    currentBuild.displayName = params.Mode == 'create' ? currentBuild.number + ' - Mode: Create' : currentBuild.number + ' - Mode: Update, Issue Key: ' + env.IssueKey
                    currentBuild.displayName = currentBuild.displayName + " , with subtasks: ${params.WithSubtasks}"

                    currentBuild.description = "Release: ${Release}, Deploy Stage: ${DeployStage}"
                }
                
                sh '''
                    cd ${WORK_DIR}
                    
                    # 创建并激活虚拟环境
                    # 检查虚拟环境是否已存在，不存在则创建
                    if [ ! -d "${VENV_PATH}" ]; then
                        echo "创建新的Python虚拟环境..."
                        python3 -m venv ${VENV_PATH}
                    else
                        echo "使用已存在的Python虚拟环境..."
                    fi
                    
                    # 激活虚拟环境
                    source ${VENV_PATH}/bin/activate
                    
                    # 安装依赖包
                    pip install -i ${PIP_MIRROR} -r requirements.txt

                    # 渲染config.py
                    sed -i "s/#RELEASE_INFO_DB_HOST#/${RELEASE_INFO_DB_HOST}/" config.py
                    sed -i "s/#RELEASE_INFO_DB_NAME#/${RELEASE_INFO_DB_NAME}/" config.py
                    sed -i "s/#RELEASE_INFO_DB_USER#/${RELEASE_INFO_DB_USER}/" config.py
                    sed -i "s/#RELEASE_INFO_DB_PASSWORD#/${RELEASE_INFO_DB_PASSWORD}/" config.py
                '''
            }
        }

        stage('预览Jira Ticket') {
            steps {
                sh '''
                    cd ${WORK_DIR}
                    
                    # 检查虚拟环境是否存在，不存在则创建
                    if [ ! -d "${VENV_PATH}" ]; then
                        echo "创建新的Python虚拟环境..."
                        python3 -m venv ${VENV_PATH}
                        source ${VENV_PATH}/bin/activate
                        pip install -i ${PIP_MIRROR} -r requirements.txt
                    else
                        echo "使用已存在的Python虚拟环境..."
                        source ${VENV_PATH}/bin/activate
                    fi
                    
                    python3 create_jira_issue.py \
                        --release "${Release}" \
                        --deploy-stage "${DeployStage}" \
                        --mode "${Mode}" \
                        --issue-key "${IssueKey}" \
                        ${WITH_SUBTASKS_PARAM} \
                        --dry-run
                 '''
            }
        }
        stage('创建或更新Jira Ticket') {
            options {
                timeout(time: 15, unit: 'MINUTES') 
            }
            input {
                message "Create/Update Jira Ticket"
                parameters {
                    booleanParam(name: 'Create/Update', defaultValue: true)
                }
            }
            steps {
                sh '''
                    cd ${WORK_DIR}
                    
                    # 检查虚拟环境是否存在，不存在则创建
                    if [ ! -d "${VENV_PATH}" ]; then
                        echo "创建新的Python虚拟环境..."
                        python3 -m venv ${VENV_PATH}
                        source ${VENV_PATH}/bin/activate
                        pip install -i ${PIP_MIRROR} -r requirements.txt
                    else
                        echo "使用已存在的Python虚拟环境..."
                        source ${VENV_PATH}/bin/activate
                    fi

                    if [ "${Mode}" == "create" ]; then
                        echo "开始创建Jira Ticket"
                    else
                        echo "开始更新Jira Ticket, issue key: ${IssueKey}"
                    fi
                    
                    python3 -u create_jira_issue.py \
                        --release "${Release}" \
                        --deploy-stage "${DeployStage}" \
                        --mode "${Mode}" \
                        --issue-key "${IssueKey}" \
                        ${WITH_SUBTASKS_PARAM}
                '''
            }
        }
    }
}