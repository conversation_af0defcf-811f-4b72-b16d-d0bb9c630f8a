# Create Release Jira Ticket 工具

该工具用于创建或更新ChinaSFA发布的Jira Ticket，支持自动创建相关子任务。

## 环境设置

### 创建Python虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/Mac
source venv/bin/activate
# Windows
# venv\Scripts\activate
```

### 安装依赖

```bash
# 安装所需的依赖包
pip install -r requirements.txt
```

依赖列表在`requirements.txt`文件中定义，包含：
- requests: 用于API调用
- jinja2: 用于模板渲染
- mysql-connector-python: 用于数据库连接

## 使用方法

### 基本使用

```bash
python create_jira_issue.py --release "25R1.0" --deploy-stage "Prod" --mode "create" --with-subtasks
```

### 参数说明

必需参数:
- `--release`: 发布版本 (例如: "25R1.0")

可选参数:
- `--deploy-stage`: 部署环境，可选值为 "Prod" 或 "Preview"，默认为 "Prod"
- `--mode`: 操作模式，可选值为 "create" 或 "update"，默认为 "create"
- `--issue-key`: Jira issue key，在更新模式下需要提供
- `--with-subtasks`: 是否创建子任务
- `--dry-run`: 预览模式，不实际创建或更新Jira ticket
- `--token`: Jira API 令牌，如未提供将使用环境变量 JIRA_TOKEN

高级参数（用于手动覆盖数据库信息）:
- `--deploy-date`: 手动指定部署日期
- `--msd-deploy-date`: 手动指定MSD部署日期
- `--services`: 手动指定服务列表，多个值用逗号分隔
- `--customers`: 手动指定客户列表，多个值用逗号分隔

## 数据库配置

脚本需要连接到Release Info数据库，请确保以下条件满足:

1. 数据库连接信息正确配置在`config.py`文件的`DB_CONFIG`中
2. 数据库中包含相应版本、环境、服务和客户的信息
3. 数据库用户有足够的权限访问相关表

## Jenkins 使用

在Jenkins中使用时，只需提供以下参数:

- `Release`: 发布版本
- `DeployStage`: 部署环境 (Prod/Preview)
- `Mode`: 操作模式 (create/update)
- `IssueKey`: 更新模式下的Jira issue key
- `WithSubtasks`: 是否创建子任务 

> **注意**: 在Jenkins执行环境中，已自动配置Python虚拟环境并安装所需依赖。Jenkinsfile中每个执行阶段都会检查虚拟环境是否存在，确保即使在不同节点上运行也能正确配置环境。虚拟环境及依赖安装都是自动完成的，无需手动配置。