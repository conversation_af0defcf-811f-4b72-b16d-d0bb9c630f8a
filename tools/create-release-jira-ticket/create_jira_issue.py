#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建JIRA工单脚本 - 使用数据库获取客户和发布信息
"""

import requests
import json
import sys
import os
import argparse
from datetime import datetime
from jinja2 import Template, StrictUndefined
import difflib
import mysql.connector
from mysql.connector import Error
import copy

# 导入配置
from config import (
    JIRA_URL,
    JIRA_API_PATH,
    JIRA_ISSUE_API,
    JIRA_CREATEMETA_API,
    DB_CONFIG
)

# 导入模板模块
from templates import (
    JIRA_TEMPLATE,
    DESCRIPTION_TEMPLATE,
    SUBTASK_TEMPLATE,
    get_subtasks
)

def get_db_connection():
    """
    获取数据库连接
    
    Returns:
        connection: 数据库连接对象
    """
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Error as e:
        print(f"数据库连接错误: {e}")
        sys.exit(1)

def get_release_info(release, deploy_stage):
    """
    从数据库获取版本信息
    
    Args:
        release: 版本号
        deploy_stage: 部署环境 (Prod/Preview)
    
    Returns:
        dict: 包含版本信息的字典
    """
    connection = get_db_connection()
    cursor = connection.cursor(dictionary=True)
    
    try:
        # 获取版本ID
        cursor.execute("SELECT id FROM release_version WHERE version = %s", (release,))
        release_version = cursor.fetchone()
        
        if not release_version:
            print(f"错误: 未找到版本 {release}")
            sys.exit(1)
            
        release_version_id = release_version['id']
        
        # 获取环境ID
        cursor.execute("SELECT id FROM environment WHERE name = %s", (deploy_stage.capitalize(),))
        environment = cursor.fetchone()
        
        if not environment:
            print(f"错误: 未找到环境 {deploy_stage}")
            sys.exit(1)
            
        environment_id = environment['id']
        
        # 获取需要部署的服务
        cursor.execute("""
            SELECT s.name 
            FROM release_service rs
            JOIN service s ON rs.service_id = s.id
            WHERE rs.release_version_id = %s AND rs.environment_id = %s
        """, (release_version_id, environment_id))
        
        services = [row['name'] for row in cursor.fetchall()]
        
        # 获取需要部署的客户/租户信息
        cursor.execute("""
            SELECT c.id, c.en_name, c.name, c.status, rt.release_date, t.deploy_window, t.name as tenant_name,
                   CASE WHEN exists (
                        SELECT 1 FROM tenant_service ts 
                        JOIN service s ON ts.service_id = s.id 
                        WHERE ts.tenant_id = t.id AND s.name = 'hydra' AND ts.is_active = true
                   ) THEN '是' ELSE '　' END as with_hydra,
                   CASE WHEN exists (
                        SELECT 1 FROM release_customer_ps_option rcp
                        WHERE rcp.customer_id = c.id AND rcp.release_version_id = %s AND rcp.deploy_ps_code = true
                   ) THEN '是' ELSE '　' END as with_ps_code
            FROM release_tenant rt
            JOIN tenant t ON rt.tenant_id = t.id
            JOIN customer c ON t.customer_id = c.id
            JOIN environment e ON t.environment_id = e.id
            WHERE rt.release_version_id = %s AND e.id = %s
        """, (release_version_id, release_version_id, environment_id))
        
        customers_info = cursor.fetchall()
        
        # 提取客户列表和构建客户详细信息
        customers = []
        customer_values = []
        
        # 按状态分组并排序：已上线 -> 准生产 -> 内部
        status_order = {'已上线': 0, '准生产': 1, '内部': 2}
        
        # 对客户信息进行排序
        sorted_customers_info = sorted(customers_info, key=lambda x: (
            status_order.get(x['status'], 999),  # 首先按状态排序
            x['id']  # 然后在每个状态组内按ID排序
        ))
        
        for customer in sorted_customers_info:
            customers.append(customer['en_name'])
            # 构建与ALL_CUSTOMERS格式相同的客户对象
            customer_obj = {
                "name": customer['name'],
                "value": customer['en_name'],
                "status": customer['status'],
                "tenant": customer['tenant_name'],
                "window": customer['deploy_window'],
                "with_hydra": customer['with_hydra'],
                "branch": "见👆tag说明",
                "ps_code": customer['with_ps_code'],
                "extra_steps": "{color:#de350b}*部署日期：{{ msd_deploy_date }}*{color}" if deploy_stage.lower() == 'prod' and customer['en_name'] == 'MSD' else "　"
            }
            customer_values.append(customer_obj)
        
        # 获取默认部署日期（从非MSD客户中选择第一个）
        deploy_date = None
        for customer in sorted_customers_info:
            if customer['en_name'] != 'MSD':
                deploy_date = customer['release_date'].strftime('%Y-%m-%d')
                break
        
        # 如果所有客户都是MSD或客户列表为空，则使用第一个客户的日期（如果有的话）
        if deploy_date is None and sorted_customers_info:
            deploy_date = sorted_customers_info[0]['release_date'].strftime('%Y-%m-%d')
        
        # 获取MSD的部署日期（如果存在）
        msd_deploy_date = None
        for customer in sorted_customers_info:
            if customer['en_name'] == 'MSD':
                msd_deploy_date = customer['release_date'].strftime('%Y-%m-%d')
                break
        
        return {
            'services': services,
            'customers': customers,
            'deploy_date': deploy_date,
            'msd_deploy_date': msd_deploy_date,
            'customer_values': customer_values
        }
        
    except Error as e:
        print(f"数据库查询错误: {e}")
        sys.exit(1)
    finally:
        cursor.close()
        connection.close()

def process_version(release):
    """
    处理版本号，生成release版本、tag版本和类型
    
    Args:
        release: 发布版本
    
    Returns:
        tuple: (release_version, tag_version, version_type)
        - release_version: 处理后的release版本, 例如: 251.0
        - tag_version: 处理后的tag版本, 例如: 251.0.0
        - version_type: 'LR' 或 'GR'
    """
    if "R" in release:
        release_version = release.replace("R", "")
    else:
        release_version = release
    
    # 判断是 LR 还是 GR
    version_parts = release_version.split(".")
    if len(version_parts) > 1 and version_parts[1] == "0":
        version_type = "GR"
    else:
        version_type = "LR"
    
    # 处理tag版本格式
    tag_version = release_version.replace("R", "") + ".0"
    
    return release_version, tag_version, version_type

def filter_fields_by_metadata_allowed_values(fieldId, values, headers):
    """
    根据metadata的allowed values筛选字段取值
    
    Args:
        fieldId: 字段id
        values: 字段取值列表（如果为None或空，则返回空列表）
    
    Returns:
        筛选后的字段取值列表（返回匹配的值的列表，如果未匹配则返回空列表）
    """
    if not values:
        print("错误: 未提供字段值或值列表为空")
        return []
    
    result = []
    
    response = requests.get(
        JIRA_CREATEMETA_API,
        headers=headers
    )
    
    if response.status_code == 200:
        metadata_list = response.json().get("values", [])
        for metadata in metadata_list:
            if metadata.get("fieldId") == fieldId:
                allowed_values = [c.get("value") for c in metadata.get("allowedValues", []) if c.get("disabled") == False]
                print(allowed_values)
                result = [c for c in values if c in allowed_values]

    return result

def render_template(release, deploy_date, msd_deploy_date, service_list, customer_list, customer_values, headers, deploy_stage="Prod"):
    """渲染模板"""

    print(f"service_list: {service_list}")
    print(f"customer_list: {customer_list}")

    release_version, tag_version, version_type = process_version(release)

    valid_customers_by_metadata = filter_fields_by_metadata_allowed_values("customfield_11032", customer_list, headers)
    
    if not valid_customers_by_metadata:
        print(f"警告: customer字段未找到任何匹配的客户。请检查客户值: {customer_list}!")
    
    print(f"customer字段匹配的客户: {valid_customers_by_metadata}")
    
    # 第一次总体渲染描述模板，然后第二次渲染出msd_deploy_date
    intermediate_description_template = Template(DESCRIPTION_TEMPLATE)
    
    try:
        intermediate_description_content = intermediate_description_template.render(
            release=release,
            release_version=release_version,
            tag_version=tag_version,
            version_type=version_type,
            deploy_date=deploy_date,
            msd_deploy_date=msd_deploy_date,
            service_list=service_list,
            customer_values=customer_values,
            deploy_stage=deploy_stage
        )
    except Exception as e:
        print(f"渲染中间描述模板时出错: {str(e)}")
        sys.exit(1)
    
    description_template = Template(intermediate_description_content)
    try:
        description_content = description_template.render(
            msd_deploy_date=msd_deploy_date
        )
    except Exception as e:
        print(f"渲染描述模板时出错: {str(e)}")
        sys.exit(1)
    
    # 渲染主模板
    template = Template(JIRA_TEMPLATE)
    context = {
        "release": release,
        "release_version": release_version,
        "tag_version": tag_version,
        "version_type": version_type,
        "deploy_stage": deploy_stage,
        "deploy_date": deploy_date,
        "description_content": description_content,
        "valid_customers_by_metadata": valid_customers_by_metadata
    }
    
    try:
        rendered = template.render(**context)
        return json.loads(rendered)
    except Exception as e:
        print(f"渲染或解析主模板时出错: {str(e)}")
        print(f"上下文数据: {json.dumps(context, ensure_ascii=False)}")
        sys.exit(1)

def create_jira_issue(issue_data, headers):
    """
    创建JIRA问题
    
    Args:
        issue_data: 问题数据
        token: JIRA API令牌
        
    Returns:
        str: 创建的问题key
    """
    
    response = requests.post(
        JIRA_ISSUE_API,
        headers=headers,
        json=issue_data
    )
    
    if response.status_code == 201:
        return response.json().get("key")
    else:
        print(f"创建JIRA问题失败: {response.status_code}")
        print(response.text)
        return None

def update_jira_issue(issue_key, issue_data, headers):
    """
    更新JIRA issue
    
    Args:
        issue_key: issue key
        issue_data: issue data
        headers: JIRA API headers
    """
    # 创建issue_data的副本，以便可以修改而不影响原始数据
    update_data = copy.deepcopy(issue_data)
    
    # 从更新数据中移除assignee字段，以保留原有的负责人
    if 'fields' in update_data and 'assignee' in update_data['fields']:
        del update_data['fields']['assignee']
    
    response = requests.put(
        f"{JIRA_ISSUE_API}/{issue_key}",
        headers=headers,
        json=update_data
    )
    
    if response.status_code == 204:
        print(f"JIRA issue已更新: {issue_key}")
    else:
        print(f"更新JIRA issue失败: {response.status_code}")
        print(response.text)

def render_subtask_templates(parent_key, release, deploy_date, msd_deploy_date, service_list, deploy_stage="Prod"):
    """
    渲染子任务模板
    
    Args:
        parent_key: 主issue的key
        release: 发布版本
        deploy_date: 部署日期
        msd_deploy_date: MSD的部署日期
        service_list: 服务列表
        deploy_stage: 部署环境 (Prod/Preview)
        
    Returns:
        list: 子任务数据列表
    """

    release_version, tag_version, version_type = process_version(release)

    result = []

    # 获取对应环境的子任务列表
    subtasks = get_subtasks(deploy_stage)
    
    # 筛选出需要渲染的子任务
    subtasks_to_render = [subtask for subtask in subtasks if subtask.get("related_service") in service_list and subtask.get("type") in ("common", version_type)]
    
    for subtask in subtasks_to_render:
        # 渲染子任务摘要，传递所有必要的变量
        summary_template = Template(subtask["summary_template"])
        summary = summary_template.render(
            release=release,
            release_version=release_version,
            deploy_date=deploy_date,
            msd_deploy_date=msd_deploy_date,
            version_type=version_type,
            tag_version=tag_version
        )
        
        # 从JSON模板获取子任务数据基础结构
        subtask_template = Template(SUBTASK_TEMPLATE)
        
        # 确定使用哪个部署日期
        task_deploy_date = deploy_date
        # 如果摘要中包含"MSD"和"部署"，则使用MSD的部署日期
        if "MSD" in summary and "部署" in summary and msd_deploy_date:
            task_deploy_date = msd_deploy_date
        
        # 准备渲染上下文数据
        context = {
            "parent_key": parent_key,
            "summary": summary,
            "issuetype_id": subtask["issuetype_id"],
            "priority_id": subtask["priority_id"],
            "deploy_date": task_deploy_date,
            "msd_deploy_date": msd_deploy_date,
            "release": release,
            "release_version": release_version,
            "tag_version": tag_version,
            "version_type": version_type,
            "assignee": subtask.get("assignee"),
            "description": subtask.get("description", "")
        }
        
        # 渲染子任务数据
        rendered = subtask_template.render(**context)
        subtask_data = json.loads(rendered)
        
        result.append(subtask_data)
    
    return result

def get_existing_subtasks(parent_key, headers):
    """
    获取父问题的所有子任务
    """
    response = requests.get(
        f"{JIRA_ISSUE_API}/{parent_key}",
        headers=headers
    )
    return response.json().get("fields", {}).get("subtasks", [])

def create_update_subtasks(parent_key, release, deploy_date, msd_deploy_date, service_list, headers, deploy_stage="Prod"):
    """
    为父问题创建子任务
    
    Args:
        parent_key: 父问题的key
        release: 发布版本
        deploy_date: 部署日期
        msd_deploy_date: MSD的部署日期
        service_list: 服务列表
        headers: JIRA API headers
        deploy_stage: 部署环境 (Prod/Preview)
    """
    release_version, tag_version, version_type = process_version(release)
    
    # 渲染子任务模板
    subtask_templates = render_subtask_templates(
        parent_key,
        release,
        deploy_date,
        msd_deploy_date,
        service_list,
        deploy_stage
    )

    existing_subtasks = get_existing_subtasks(parent_key, headers)
    existing_subtask_info = []
    if existing_subtasks:
        for subtask in existing_subtasks:
            subtask_key = subtask.get("key")
            subtask_summary = subtask.get("fields", {}).get("summary", "")
            existing_subtask_info.append({
                "key": subtask_key,
                "summary": subtask_summary
            })
        # 打印现有子任务列表
        print(f"subtask信息: {json.dumps(existing_subtask_info, indent=2, ensure_ascii=False)}")
    else:
        print(f"当前issue {parent_key} 没有子任务")
    
    for subtask in subtask_templates:
        subtask_summary = subtask.get("fields", {}).get("summary", "")
        max_similarity_subtask_key = None
        max_similarity_subtask_summary = None
        max_similarity = 0

        print(f"开始处理子任务:{subtask_summary}")
        # 找到相似度最高的子任务
        for info in existing_subtask_info:
            info_summary = info.get("summary", "")
            string_similarity = difflib.SequenceMatcher(None, subtask_summary, info_summary).ratio()
            if string_similarity > max_similarity:
                max_similarity = string_similarity
                max_similarity_subtask_key = info.get("key")
                max_similarity_subtask_summary = info.get("summary")

        if max_similarity > 0.8:
            print(f"找到最大相似度{max_similarity}的子任务:{max_similarity_subtask_key} {max_similarity_subtask_summary}")
            
            # 创建subtask的副本，移除assignee字段以保留原有的负责人
            update_subtask = copy.deepcopy(subtask)
            if 'fields' in update_subtask and 'assignee' in update_subtask['fields']:
                del update_subtask['fields']['assignee']
                
            response = requests.put(
                f"{JIRA_ISSUE_API}/{max_similarity_subtask_key}",
                headers=headers,
                json=update_subtask
            )

            if response.status_code == 204:
                print(f"子任务已更新: {max_similarity_subtask_key}")
            else:
                print(f"更新子任务失败: {response.status_code}")
                print(response.text)      
        else:
            print(f"已有任务与当前任务最大相似度为{max_similarity}，判定为不同任务，开始创建")
            
            response = requests.post(
                JIRA_ISSUE_API,
                headers=headers,
                json=subtask
            )
            
            if response.status_code == 201:
                subtask_key = response.json().get("key")
                print(f"子任务已创建: {subtask_key}")
            else:
                print(f"创建子任务失败: {response.status_code}")
                print(response.text)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="创建/更新JIRA issue")
    parser.add_argument("--release", required=True, help="发布版本")
    parser.add_argument("--deploy-stage", default="prod", choices=["Prod", "Preview"], help="部署环境 (Prod/Preview)")
    parser.add_argument("--with-subtasks", action="store_true", help="创建子任务")
    parser.add_argument("--mode", choices=["create", "update"], default="create", help="操作模式: create/update，默认create")
    parser.add_argument("--issue-key", help="JIRA issue key，更新时需要指定")
    parser.add_argument("--token", help="JIRA API token")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不实际创建issue")
    
    # 以下参数保留用于向后兼容和手动覆盖
    parser.add_argument("--deploy-date", help="部署日期（可选，如未指定将从数据库获取）")
    parser.add_argument("--msd-deploy-date", help="MSD部署日期（可选，如未指定将从数据库获取）")
    parser.add_argument("--services", help="服务列表，多个值用逗号分隔（可选，如未指定将从数据库获取）")
    parser.add_argument("--customers", help="客户列表，多个值用逗号分隔（可选，如未指定将从数据库获取）")

    args = parser.parse_args()

    if not args.token:
        TOKEN = os.getenv("JIRA_TOKEN")
        if not TOKEN:
            print("错误: 未提供JIRA令牌。请使用--token参数或设置JIRA_TOKEN环境变量")
            sys.exit(1)
    else:
        TOKEN = args.token

    HEADERS = {
        "Authorization": f"Bearer {TOKEN}",
        "Content-Type": "application/json"
    }
    
    RELEASE = args.release
    DEPLOY_STAGE = args.deploy_stage
    
    # 从数据库获取部署信息
    release_info = get_release_info(RELEASE, DEPLOY_STAGE)
    print(f"release_info: {json.dumps(release_info, indent=2, ensure_ascii=False)}")
    
    # 允许命令行参数覆盖数据库值（向后兼容和紧急情况）
    DEPLOY_DATE = args.deploy_date if args.deploy_date else release_info['deploy_date']
    MSD_DEPLOY_DATE = args.msd_deploy_date if args.msd_deploy_date else release_info['msd_deploy_date']
    
    SERVICE_LIST = []
    if args.services:
        SERVICE_LIST = [sv.strip() for sv in args.services.split(",")]
    else:
        SERVICE_LIST = release_info['services']
        
    CUSTOMER_LIST = []
    if args.customers:
        CUSTOMER_LIST = [cv.strip() for cv in args.customers.split(",")]
    else:
        CUSTOMER_LIST = release_info['customers']
    
    # 获取客户详细信息
    CUSTOMER_VALUES = release_info.get('customer_values', [])
    
    try:
        # 渲染JIRA问题主体，并创建问题
        issue_data = render_template(RELEASE, DEPLOY_DATE, MSD_DEPLOY_DATE, SERVICE_LIST, CUSTOMER_LIST, CUSTOMER_VALUES, HEADERS, DEPLOY_STAGE)
        
        if args.dry_run:
            # 预览模式
            print("预览JIRA问题数据:")
            print(json.dumps(issue_data, indent=2, ensure_ascii=False))
            
            # 如果指定了--with-subtasks参数，打印子任务模板
            if args.with_subtasks:
                # 渲染子任务模板
                subtask_templates = render_subtask_templates(
                    "PLACEHOLDER-KEY",  # 在预览模式下使用占位符
                    RELEASE,
                    DEPLOY_DATE,
                    MSD_DEPLOY_DATE,
                    SERVICE_LIST,
                    DEPLOY_STAGE
                )
                
                print("\n预览子任务数据:")
                for i, subtask in enumerate(subtask_templates):
                    print(f"\n子任务 #{i+1}:")
                    print(json.dumps(subtask, indent=2, ensure_ascii=False))
        else:
            if args.mode == "create":
                # 创建issue
                issue_key = create_jira_issue(issue_data, HEADERS)
                print(f"JIRA问题已创建: {issue_key}")
                
                # 如果指定了--with-subtasks参数，创建子任务
                if args.with_subtasks and issue_key:
                    create_update_subtasks(issue_key, RELEASE, DEPLOY_DATE, MSD_DEPLOY_DATE, SERVICE_LIST, HEADERS, DEPLOY_STAGE)
            elif args.mode == "update":
                # 更新issue
                if not args.issue_key:
                    print("错误: 更新时需要指定JIRA issue key。请使用--issue-key参数指定")
                    sys.exit(1)
                print(f"更新JIRA issue: {args.issue_key}")
                update_jira_issue(args.issue_key, issue_data, HEADERS)
                # 更新子任务
                create_update_subtasks(args.issue_key, RELEASE, DEPLOY_DATE, MSD_DEPLOY_DATE, SERVICE_LIST, HEADERS, DEPLOY_STAGE)
            else:
                print("错误: 无效的操作模式。请使用--mode参数指定操作模式: create/update")
                sys.exit(1)
    
    except Exception as e:
        print(f"创建JIRA问题时出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 