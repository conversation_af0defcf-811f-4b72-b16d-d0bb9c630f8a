pipeline {
    agent any

    stages {
        stage('校验并预览请求') {
            steps {
                script {
                    /* 1. 必填校验 */
                    if (!params.Version?.trim()) {
                        error '版本号不能为空'
                    }
                    
                    /* 检查是否选择服务 */
                    if (!params.Service || params.Service.size() == 0) {
                        error '至少需要选择一个服务'
                    }
                    
                    /* 检查是否包含crm服务 */
                    if (!params.Service.contains('crm')) {
                        error '必须选择crm服务'
                    }

                    /* 设置job description */
                    currentBuild.description = "Version: ${params.Version}, Service: ${params.Service}"

                    /* 2. 组装 envVersions */
                    def envVersions = [:]
                    def version = params.Version.trim()
                    
                    services = params.Service.split(',')
                    // 处理多选参数
                    services.each { service ->
                        envVersions[service] = version
                    }
                    
                    echo "envVersions: ${envVersions}"

                    /* 3. 生成 JSON 并保存到全局环境变量，方便后续 stage 使用 */
                    def payload = groovy.json.JsonOutput.toJson([env_versions: envVersions])
                    env.JSON_PAYLOAD = payload

                    /* 4. 打印美化后的预览 */
                    echo "------ 本次将提交的 JSON ------"
                    echo groovy.json.JsonOutput.prettyPrint(payload)
                    echo "--------------------------------"
                }
            }
        }

        stage('发送请求') {
            options {
                timeout(time: 15, unit: 'MINUTES') 
            }
            input {
                message "确定发送请求吗？"
                parameters {
                    booleanParam(name: 'SendRequest', defaultValue: true)
                }
            }
            steps {
                script{
                    if (SendRequest) {
                        sh '''
                            curl -v --fail -X POST \
                              -H "Host: ${EM_HOST}" \
                              -H "Content-Type: application/json" \
                              -d "${JSON_PAYLOAD}" \
                              http://${INC_NLB_DNS_NAME}/private-api/preview/log
                        '''
                    }
                }
            }
        }
    }
}