pipeline {
    agent any
    environment {
        gitlabTokenCredId = 'gitlab-https-deploy-token'
    }
     stages {
        stage('prepare') {
            steps {
                dir('tools/release-rc-history') {
                    script {
                        echo "安装依赖包"
                        sh '''
                            pip3 install -i https://mirrors.devops.veevasfa.cn/repository/pypi-all/simple -r requirements.txt 
                        '''
                        echo "渲染配置文件"
                        withCredentials([usernamePassword(credentialsId: gitlabTokenCredId, usernameVariable: 'GITLAB_USER', passwordVariable: 'GITLAB_TOKEN')]) {
                            sh '''
                                cp gitlab.cfg.template gitlab.cfg
                                sed -i "s/#GITLAB_TOKEN#/${GITLAB_TOKEN}/g" gitlab.cfg
                            '''
                        }
                    }
                }
            }
        }
        stage('get rc history jira id') {
            steps {
                dir('tools/release-rc-history'){  
                    script {
                        def versions = env.release_versions.split(',')
                        versions.each { version ->
                            echo "获取版本 ${version} RC后commits对应的jira id"
                            sh "python3 -u list_jira_ids.py -p ${project_id} -r ${version}"
                        }
                    }
                }
            }
        }
    }
}