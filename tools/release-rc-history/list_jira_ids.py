import argparse
import gitlab
import re
import json

parser = argparse.ArgumentParser(description='输入仓库信息')
parser.add_argument('--project-id', '-p', required=True, help='gitlab项目id')
parser.add_argument('--release-version', '-r', required=True, help='release版本号')

args = parser.parse_args()

project_id = args.project_id
release_version = args.release_version

RELEASE_BRANCH_PREFIX = 'release/'
LR_TAG_PREFIX = 'LR/'
GR_TAG_PREFIX = 'GR/'

# 初始化gitlab client
gl = gitlab.Gitlab.from_config('veevadev', ['gitlab.cfg'])
project = gl.projects.get(project_id)

# 根据版本号区分LR、GR
if release_version.split('.')[1] == '0':
    RELEASE_TAG_NAME = GR_TAG_PREFIX + release_version + '.0'
    RC1_TAG_NAME = GR_TAG_PREFIX + release_version + '.0-rc1'
else:
    RELEASE_TAG_NAME = LR_TAG_PREFIX + release_version + '.0'
    RC1_TAG_NAME = LR_TAG_PREFIX + release_version + '.0-rc1'
RELEASE_BRANCH_NAME = RELEASE_BRANCH_PREFIX + release_version
print('release tag号：' + RELEASE_TAG_NAME)
print('rc1 tag号：' + RC1_TAG_NAME)
print('release分支名：' + RELEASE_BRANCH_NAME)

# 获取主分支上在RC1_TAG_NAME和RELEASE_TAG_NAME之间的commit
rc1_commit_ts = project.tags.get(RC1_TAG_NAME).commit['created_at']
release_commit_ts = project.tags.get(RELEASE_TAG_NAME).commit['created_at']
print('rc1 tag创建时间：' + rc1_commit_ts)
print('release tag创建时间：' + release_commit_ts)

# 获取合并到release分支rc1 tag和release tag之间的merge requests
merges = project.mergerequests.list(state='merged', target_branch=RELEASE_BRANCH_NAME, created_after=rc1_commit_ts, created_before=release_commit_ts, iterator=True)

# 遍历merges, 提取mr关联的commits, 提取commit title中的JIRA ID
jira_ids = []
for merge in merges:
    print(merge.title)
    commits = merge.commits()
    print('mr关联commits数量：' + str(len(commits)))
    for commit in commits:
        pattern = r'^(ORI-\d+)'
        match = re.search(pattern, commit.title)
        if match:
            jira_ids.append(match.group(1))
print('共有' + str(len(merges)) + '个merge requests')
# 去重
jira_ids = list(set(jira_ids))
print('共有' + str(len(jira_ids)) + '个JIRA ID')
print(release_version + ' code freeze后commits的JIRA ID列表：' + '\n' + str(jira_ids))