def node_label = NodeLabel ? NodeLabel : 'master'
pipeline {
    agent { label node_label }
    stages {
        stage("准备环境"){
            steps{
                script{
                    switch(MailType) {
                        case 'preview_done':
                            def params_list = Parameters.split('\\,')
                            env.ReleaseVersion = params_list[0]
                            break
                        case 'lr_before':
                            def params_list = Parameters.split('\\,')
                            env.ReleaseVersion = params_list[0]
                            env.ReleaseDate = params_list[1]
                            env.ReleaseDateEng = params_list[2..3].join(',')
                            env.ReleaseTime = params_list[4]
                            env.LRReleaseStartTime = params_list[5]
                            env.GrReleaseDate = params_list[6]
                            env.GrReleaseDateEng = params_list[7..8].join(',')
                            env.GrVersion = params_list[9]
                            break
                        case 'lr_after':
                            def params_list = Parameters.split('\\,')
                            env.ReleaseVersion = params_list[0]
                            env.ReleaseDate = params_list[1]
                            break
                        case 'gr_before':
                            def params_list = Parameters.split('\\,')
                            env.ReleaseVersion = params_list[0]
                            env.ReleaseDate = params_list[1]
                            env.ReleaseDateEng = params_list[2..3].join(',')
                            env.ReleaseTime = params_list[4]
                            env.GRReleaseStartTime = params_list[5]
                            break
                        case 'gr_after':
                            def params_list = Parameters.split('\\,')
                            env.ReleaseVersion = params_list[0]
                            env.ReleaseDate = params_list[1]
                            break
                    }
                    echo "邮件类型：${MailType}"
                    sh ''' 
                            cd source
                            
                            # 创建并激活虚拟环境
                            python3 -m venv .venv
                            source .venv/bin/activate
                            
                            # 安装依赖
                            pip3 install -i https://mirrors.devops.veevasfa.cn/repository/pypi-all/simple -r requirements.txt
                            
                            cp ../mail_templates/${MailType}.html mail_template.html
                            echo "渲染邮件模板"
                            sed -i "s/#RELEASE_VERSION#/${ReleaseVersion}/g" mail_template.html
                            sed -i "s/#RELEASE_DATE#/${ReleaseDate}/g" mail_template.html
                            sed -i "s/#PRE_RELEASE_DATE#/${PreReleaseDate}/g" mail_template.html
                            sed -i "s/#RELEASE_DATE_ENG#/${ReleaseDateEng}/g" mail_template.html
                            sed -i "s/#PRE_RELEASE_DATE_ENG#/${PreReleaseDateEng}/g" mail_template.html
                            sed -i "s/#RELEASE_TIME#/${ReleaseTime}/g" mail_template.html
                            sed -i "s/#GR_RELEASE_DATE#/${GrReleaseDate}/g" mail_template.html
                            sed -i "s/#GR_RELEASE_DATE_ENG#/${GrReleaseDateEng}/g" mail_template.html
                            sed -i "s/#GR_VERSION#/${GrVersion}/g" mail_template.html
                       '''
                }
            }
        }
        stage("预览") {
            options {
                timeout(time: 15, unit: 'MINUTES') 
            }
            input {
                message "发送邮件给 ${Preview_Receivers}以预览?" 
                parameters {
                    booleanParam(name: 'Preview', defaultValue: true)
                }
            }
            steps{
                script{
                    wrap([$class: "MaskPasswordsBuildWrapper", varPasswordPairs: [[password: Password]]]){
                        if (Preview) {
                            sh '''
                                cd source
                                source .venv/bin/activate
                                
                                cp mail_template.html mail_template_render.html
                                python3 redmailsend.py -r $Preview_Receivers -t '' -c '' -b '' -u $Username -p $Password 
                            '''
                        }
                    }
                }
            }
        }
        stage("发送Release邮件") {
            options {
                timeout(time: 15, unit: 'MINUTES') 
            }
            input {
                message "确定发送邮件给：租户：$Tenants 并bcc给$Bcc?" 
                parameters {
                    booleanParam(name: 'SendMail', defaultValue: true)
                }
            }
            steps{
                script{
                    wrap([$class: "MaskPasswordsBuildWrapper", varPasswordPairs: [[password: Password]]]){
                        if (SendMail) {
                            sh '''
                            cd source
                            source .venv/bin/activate
                            
                            tenants=( `echo $Tenants | tr ',' ' '` )
                            echo "租户列表: ${tenants[@]}"
                            for tenant in  ${tenants[@]}
                            do
                                echo "当前租户：$tenant"
                                
                                echo "渲染最终邮件模板"
                                cp mail_template.html mail_template_render.html
                                sed -i "s/#TENANT#/${tenant}/g" mail_template_render.html
                                
                                python3 redmailsend.py -r '' -t $tenant -c '' -b $Bcc -u $Username -p $Password
                                echo "给$tenant发送邮件完成！"
                            done 
                            echo "发送邮件完成！"
                            '''
                        }
                    }
                }
            }
        }
    }
}
