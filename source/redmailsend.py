from operator import itemgetter
from platform import release
from redmail import EmailSender
import yaml
import argparse
import os

parser = argparse.ArgumentParser(description='输入收件人')
parser.add_argument('--previewers', '-r', help='预览用户')
parser.add_argument('--to', '-t', help='租户')
parser.add_argument('--cc', '-c', help='cc收件人列表，字符串，以,分割')
parser.add_argument('--bcc', '-b', help='bcc收件人列表，字符串，以,分割')
parser.add_argument('--username', '-u', help='发件邮箱账号')
parser.add_argument('--password', '-p', help='发件邮箱密码')

args = parser.parse_args()

to = args.to
previewers = args.previewers

if to:
    to_yaml = open('receivers.yaml', 'r')
    to_map = yaml.load(to_yaml, Loader=yaml.FullLoader)
    mail_to = to_map[to]
else:
    mail_to=''

if previewers:
    mail_to = previewers.split(',')

mail_cc = args.cc.split(',')
mail_bcc = args.bcc.split(',')
print('to列表: {0}\ncc列表: {1}\nbcc列表: {2}'.format(mail_to, mail_cc, mail_bcc))

email = EmailSender(
    host='smtp.qiye.aliyun.com',
    port='25',
    username=args.username,
    password=args.password
)
print('发件人邮箱：', email.user_name)


mail_type = os.getenv('MailType')
# release_version = os.getenv('ReleaseVersion')

subjects = {
    'preview_done': 'Veeva China CRM Suite 产品新版本 %s 开放预览与验证通知' %(os.getenv('ReleaseVersion')),
    'lr_before': 'Veeva China CRM Suite 产品新版本 %s 发布预通知 (%s %s)' %(os.getenv('ReleaseVersion'), os.getenv('ReleaseDate'), os.getenv('LRReleaseStartTime')),
    'lr_after': 'Veeva China CRM Suite 产品新版本 %s 发布完成通知 (%s)' %(os.getenv('ReleaseVersion'), os.getenv('ReleaseDate')), 
    'gr_before': 'Veeva China CRM Suite 产品新版本 %s 发布预通知 (%s %s)' %(os.getenv('ReleaseVersion'), os.getenv('ReleaseDate'),os.getenv('GRReleaseStartTime')),
    'gr_after': 'Veeva China CRM Suite 产品新版本 %s 发布完成通知 (%s)' %(os.getenv('ReleaseVersion'), os.getenv('ReleaseDate')),
    'infra_change': 'Veeva China CRM Suite 预部署环境下线通知'
}
sender_display_name = 'Veeva China CRM Suite Notifications <' + email.username + '>'

email.set_template_paths(html='./')
msg = email.send(
    subject=subjects[mail_type],
    sender=sender_display_name,
    receivers=mail_to,
    cc=mail_cc,
    bcc=mail_bcc,
    html_template='mail_template_render.html',
    body_images={'banner_up': 'up.png', 'banner_down': 'down.png'}
)
